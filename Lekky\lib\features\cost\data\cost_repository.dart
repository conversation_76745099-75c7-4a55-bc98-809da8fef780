// File: lib/features/cost/data/cost_repository.dart
import '../../../core/utils/logger.dart';

import '../../../core/di/service_locator.dart';
import '../../averages/domain/services/average_service.dart';
import '../../averages/domain/repositories/per_reading_average_repository.dart';
import '../domain/services/hybrid_cost_service.dart';
import '../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../top_ups/domain/repositories/top_up_repository.dart';
import '../domain/models/cost_period.dart';
import '../domain/models/cost_result.dart';
import '../presentation/models/chart_data.dart';
import '../../../core/constants/preference_keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Repository for cost-related operations
class CostRepository {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;

  final PerReadingAverageRepository _perReadingAverageRepository;
  final HybridCostService _hybridCostService;

  /// Constructor
  CostRepository({
    MeterReadingRepository? meterReadingRepository,
    TopUpRepository? topUpRepository,
    PerReadingAverageRepository? perReadingAverageRepository,
    HybridCostService? hybridCostService,
  })  : _meterReadingRepository =
            meterReadingRepository ?? serviceLocator<MeterReadingRepository>(),
        _topUpRepository = topUpRepository ?? serviceLocator<TopUpRepository>(),
        _perReadingAverageRepository = perReadingAverageRepository ??
            serviceLocator<PerReadingAverageRepository>(),
        _hybridCostService =
            hybridCostService ?? serviceLocator<HybridCostService>();

  /// Calculate historic cost using hybrid approach
  Future<double> calculateHistoricCostUsingStoredAverages(
      DateTime fromDate, DateTime toDate) async {
    try {
      Logger.info(
          'CostRepository: Using hybrid cost calculation for period ${fromDate.toIso8601String()} to ${toDate.toIso8601String()}');
      return await _hybridCostService.calculateCustomPeriodCost(
          fromDate, toDate);
    } catch (e) {
      Logger.error(
          'CostRepository: Failed to calculate historic cost using hybrid service: $e');
      return calculateUsingRawMeterReadings(fromDate, toDate);
    }
  }

  /// Fallback calculation using raw meter readings
  Future<double> calculateUsingRawMeterReadings(
      DateTime fromDate, DateTime toDate) async {
    try {
      final averageService = serviceLocator<AverageService>();
      final result = await averageService.getAverages();
      final days = toDate.difference(fromDate).inDays; // Remove +1
      return result.totalAverage * days;
    } catch (e) {
      Logger.error('Failed to calculate using raw meter readings: $e');
      return 0.0;
    }
  }

  /// Calculate the cost for a specific period
  Future<CostResult> calculateCostForPeriod(
      CostPeriod period, DateTime? fromDate, DateTime? toDate) async {
    try {
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      final topUps = await _topUpRepository.getAllTopUps();

      Logger.info(
          'CostRepository: Found ${meterReadings.length} meter readings and ${topUps.length} top-ups');

      double averageUsage = 0.0;
      double? topUpAmount;
      DateTime? topUpDate;

      final meterUnit = await getMeterUnit();

      double costPerPeriod;

      final effectiveFromDate =
          fromDate ?? DateTime.now().subtract(Duration(days: period.days));
      final effectiveToDate = toDate ?? DateTime.now();

      try {
        final averageService = serviceLocator<AverageService>();
        final result = await averageService.getAverages();
        averageUsage = result.totalAverage;
      } catch (e) {
        if (meterReadings.length >= 2) {
          final sortedReadings = List.from(meterReadings)
            ..sort((a, b) => a.date.compareTo(b.date));
          final firstReading = sortedReadings.first;
          final lastReading = sortedReadings.last;
          final totalDays =
              lastReading.date.difference(firstReading.date).inDays;

          if (totalDays > 0) {
            double totalTopUps = 0;
            for (var topUp in topUps) {
              if (topUp.date.isAfter(firstReading.date) &&
                  topUp.date.isBefore(lastReading.date)) {
                totalTopUps += topUp.amount;
              }
            }
            final totalUsage =
                firstReading.value - lastReading.value + totalTopUps;
            if (totalUsage > 0) {
              averageUsage = totalUsage / totalDays;
            }
          }
        }
      }

      int actualDays;
      if (period == CostPeriod.custom && fromDate != null && toDate != null) {
        costPerPeriod =
            await calculateHistoricCostUsingStoredAverages(fromDate, toDate);
        actualDays = toDate.difference(fromDate).inDays;
        averageUsage = actualDays > 0 ? costPerPeriod / actualDays : 0.0;
      } else {
        // Use calendar-aware calculation for month/year periods
        actualDays =
            period.calculateActualDays(fromDate: fromDate, toDate: toDate);
        costPerPeriod = averageUsage * actualDays;
      }
      Logger.info(
          'CostRepository: Final cost calculation - Average usage: $averageUsage, Actual days: $actualDays, Cost per period: $costPerPeriod');

      if (topUps.isNotEmpty) {
        final latestTopUp = topUps.first; // Already sorted by date DESC
        topUpAmount = latestTopUp.amount;
        topUpDate = latestTopUp.date;
      }

      final dailyRate = averageUsage; // Daily rate is the average usage per day

      return CostResult(
        averageUsage: averageUsage,
        costPerPeriod: costPerPeriod,
        period: period,
        meterUnit: meterUnit,
        actualDays: actualDays,
        dailyRate: dailyRate,
        topUpAmount: topUpAmount,
        topUpDate: topUpDate,
        fromDate: effectiveFromDate,
        toDate: effectiveToDate,
      );
    } catch (e) {
      Logger.error('Failed to calculate cost for period: $e');
      return CostResult(
        averageUsage: 0.0,
        costPerPeriod: 0.0,
        period: period,
        meterUnit: await getMeterUnit(),
        actualDays: 0,
        dailyRate: 0.0,
        fromDate: fromDate,
        toDate: toDate,
      );
    }
  }

  /// Get the meter unit from user preferences
  Future<String> getMeterUnit() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(PreferenceKeys.currencySymbol) ?? '₦';
    } catch (e) {
      Logger.error('Failed to get currency symbol: $e');
      return '₦'; // Default fallback
    }
  }

  /// Get the total average usage using AverageService with fallback
  Future<double> getTotalAverageUsage() async {
    try {
      final averageService = serviceLocator<AverageService>();
      final result = await averageService.getAverages();
      Logger.info(
          'CostRepository: Got total average from database: ${result.totalAverage}');
      return result.totalAverage;
    } catch (e) {
      Logger.error('CostRepository: Failed to get averages from service: $e');
      return await _calculateTotalAverageFallback();
    }
  }

  /// Fallback method for calculating total average
  Future<double> _calculateTotalAverageFallback() async {
    try {
      Logger.info('CostRepository: Using fallback calculation');

      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      final topUps = await _topUpRepository.getAllTopUps();

      if (meterReadings.length < 2) {
        return 0.0;
      }

      final sortedReadings = List.from(meterReadings)
        ..sort((a, b) => a.date.compareTo(b.date));
      final firstReading = sortedReadings.first;
      final lastReading = sortedReadings.last;
      final totalDays = lastReading.date.difference(firstReading.date).inDays;

      if (totalDays > 0) {
        double totalTopUps = 0;
        for (var topUp in topUps) {
          if (topUp.date.isAfter(firstReading.date) &&
              topUp.date.isBefore(lastReading.date)) {
            totalTopUps += topUp.amount;
          }
        }
        final totalUsage = firstReading.value - lastReading.value + totalTopUps;
        if (totalUsage > 0) {
          return totalUsage / totalDays;
        }
      }
      return 0.0;
    } catch (e) {
      Logger.error('CostRepository: Fallback calculation failed: $e');
      return 0.0;
    }
  }

  /// Get first meter reading date
  Future<DateTime?> getFirstMeterReadingDate() async {
    try {
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      if (meterReadings.isEmpty) return null;
      final sortedReadings = List.from(meterReadings)
        ..sort((a, b) => a.date.compareTo(b.date));
      return sortedReadings.first.date;
    } catch (e) {
      Logger.error('Failed to get first meter reading date: $e');
      return null;
    }
  }

  /// Get last meter reading date
  Future<DateTime?> getLastMeterReadingDate() async {
    try {
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      if (meterReadings.isEmpty) return null;
      final sortedReadings = List.from(meterReadings)
        ..sort((a, b) => b.date.compareTo(a.date));
      return sortedReadings.first.date;
    } catch (e) {
      Logger.error('Failed to get last meter reading date: $e');
      return null;
    }
  }

  /// Get previous meter reading date (second-to-last chronologically)
  Future<DateTime?> getPreviousMeterReadingDate() async {
    try {
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      if (meterReadings.length < 2) return null;
      final sortedReadings = List.from(meterReadings)
        ..sort((a, b) => b.date.compareTo(a.date));
      return sortedReadings[1].date;
    } catch (e) {
      Logger.error('Failed to get previous meter reading date: $e');
      return null;
    }
  }

  /// Check if there are sufficient meter readings (at least 2)
  Future<bool> hasSufficientMeterReadings() async {
    try {
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      return meterReadings.length >= 2;
    } catch (e) {
      Logger.error('Failed to check sufficient meter readings: $e');
      return false;
    }
  }

  /// Get recent average chart data for visualization
  Future<List<ChartData>> getRecentAverageChartData(
      DateTime? fromDate, DateTime? toDate) async {
    try {
      List<dynamic> averages;
      if (fromDate != null && toDate != null) {
        averages = await _perReadingAverageRepository
            .getPerReadingAveragesForDateRange(fromDate, toDate);
      } else {
        averages =
            await _perReadingAverageRepository.getAllPerReadingAverages();
      }
      return averages
          .map((avg) => ChartData(
                date: avg.readingDate,
                usage: avg.recentAveragePerDay,
                cost: avg.recentAveragePerDay,
              ))
          .toList();
    } catch (e) {
      Logger.error('Failed to get recent average chart data: $e');
      return [];
    }
  }

  /// Get recent average chart data for all averages (last 100)
  Future<List<ChartData>> getRecentAverageChartDataForAllAverages() async {
    try {
      final averages =
          await _perReadingAverageRepository.getRecentPerReadingAverages(100);
      return averages
          .map((avg) => ChartData(
                date: avg.readingDate,
                usage: avg.recentAveragePerDay,
                cost: avg.recentAveragePerDay,
              ))
          .toList();
    } catch (e) {
      Logger.error(
          'Failed to get recent average chart data for all averages: $e');
      return [];
    }
  }
}
