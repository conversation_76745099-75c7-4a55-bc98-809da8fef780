// File: lib/features/cost/domain/models/cost_result.dart
import 'cost_period.dart';

/// Result of a cost calculation
class CostResult {
  final double averageUsage;
  final double costPerPeriod;
  final CostPeriod period;
  final String meterUnit;
  final double? topUpAmount; // Amount topped up during the period
  final DateTime? topUpDate; // Date of the top-up
  final double? initialCredit; // Initial credit when moving in
  final DateTime? fromDate; // Start date of the period
  final DateTime? toDate; // End date of the period
  final int actualDays; // Actual number of days used in calculation
  final double dailyRate; // Cost per day

  const CostResult({
    required this.averageUsage,
    required this.costPerPeriod,
    required this.period,
    required this.meterUnit,
    required this.actualDays,
    required this.dailyRate,
    this.topUpAmount,
    this.topUpDate,
    this.initialCredit,
    this.fromDate,
    this.toDate,
  });

  /// Creates a copy of this CostResult with the given fields replaced with the new values
  CostResult copyWith({
    double? averageUsage,
    double? costPerPeriod,
    CostPeriod? period,
    String? meterUnit,
    int? actualDays,
    double? dailyRate,
    double? topUpAmount,
    DateTime? topUpDate,
    double? initialCredit,
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    return CostResult(
      averageUsage: averageUsage ?? this.averageUsage,
      costPerPeriod: costPerPeriod ?? this.costPerPeriod,
      period: period ?? this.period,
      meterUnit: meterUnit ?? this.meterUnit,
      actualDays: actualDays ?? this.actualDays,
      dailyRate: dailyRate ?? this.dailyRate,
      topUpAmount: topUpAmount ?? this.topUpAmount,
      topUpDate: topUpDate ?? this.topUpDate,
      initialCredit: initialCredit ?? this.initialCredit,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
    );
  }

  /// Format the cost per period as a string
  String get formattedCostPerPeriod =>
      '$meterUnit ${costPerPeriod.toStringAsFixed(2)}';

  /// Format the average usage as a string
  String get formattedAverageUsage =>
      '$meterUnit${averageUsage.toStringAsFixed(2)}/day';

  /// Format the top-up amount as a string
  String? get formattedTopUpAmount => topUpAmount != null
      ? '$meterUnit ${topUpAmount!.toStringAsFixed(2)}'
      : null;

  /// Check if there was a top-up during the period
  bool get hasTopUp => topUpAmount != null && topUpAmount! > 0;

  /// Check if there was initial credit when moving in
  bool get hasInitialCredit => initialCredit != null && initialCredit! > 0;

  /// Get the net cost (can be negative if there's initial credit)
  double get netCost {
    double result = costPerPeriod;
    if (hasInitialCredit) {
      result -= initialCredit!;
    }
    return result;
  }

  /// Format the net cost as a string (including credit indicator if negative)
  String get formattedNetCost {
    final cost = netCost;
    if (cost < 0) {
      return '$meterUnit ${(-cost).toStringAsFixed(2)} (credit)';
    } else {
      return '$meterUnit ${cost.toStringAsFixed(2)}';
    }
  }

  /// Get formatted description showing days, daily rate and average type
  String getFormattedDescription(String averageType) {
    final dayText = actualDays == 1 ? 'day' : 'days';
    final formattedDailyRate = '$meterUnit${dailyRate.toStringAsFixed(2)}';
    final averageTypeText =
        averageType == "Total Average" ? "Total Average" : "Historical Data";

    return '$actualDays $dayText @ $formattedDailyRate/day (Based on $averageTypeText)';
  }

  /// Format the initial credit as a string
  String? get formattedInitialCredit => initialCredit != null
      ? '$meterUnit ${initialCredit!.toStringAsFixed(2)}'
      : null;

  /// Check if this result has date range information
  bool get hasDateRange => fromDate != null && toDate != null;
}
